<div class="partnership-header" [style.background]="'linear-gradient(135deg, ' + gradientStart + ' 0%, ' + gradientEnd + ' 100%)'">
  <div class="partnership-content">
    <div class="partnership-logo">
      <ion-img [alt]="config.logoAlt" [src]="config.logoSrc"></ion-img>
    </div>
    <div class="partnership-text">
      <h2>{{ config.title }}</h2>
      <p>{{ config.description }}</p>
    </div>
  </div>

  <!-- Promo Code Section -->
  <div class="promo-code-section">
    <div class="promo-code-card">
      <div class="promo-code-content">
        <span class="promo-label">Промокод:</span>
        <span class="promo-code">{{ config.promoCode }}</span>
      </div>
      <div class="promo-actions">
        <ion-button
          (click)="copyPromoCode()"
          color="light"
          fill="outline"
          size="small">
          <ion-icon name="copy-outline" slot="start"></ion-icon>
          {{ config.copyButtonText || 'Копирай кода' }}
        </ion-button>
        <ion-button
          (click)="visitStore()"
          color="secondary"
          fill="solid"
          size="small">
          <ion-icon name="storefront-outline" slot="start"></ion-icon>
          {{ config.visitStoreButtonText || 'Посети магазина' }}
        </ion-button>
      </div>
    </div>
  </div>
</div>

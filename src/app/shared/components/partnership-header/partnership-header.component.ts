import { Component, Input, Output, EventEmitter } from '@angular/core';
import { PartnershipConfig } from './partnership-header.model';
import { ToastService } from '../../services';

@Component({
  selector: 'mpg-partnership-header',
  templateUrl: './partnership-header.component.html',
  styleUrls: ['./partnership-header.component.scss'],
})
export class PartnershipHeaderComponent {
  @Input() config!: PartnershipConfig;
  @Input() gradientStart: string = 'var(--ion-color-primary)';
  @Input() gradientEnd: string = 'var(--ion-color-dark)';
  
  @Output() promoCodeCopied = new EventEmitter<void>();
  @Output() storeVisited = new EventEmitter<void>();

  constructor(private toastService: ToastService) {}

  async copyPromoCode() {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(this.config.promoCode);
        this.toastService.showInfoToast(
          'nutrition.product-promotions.code-copied',
        );
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = this.config.promoCode;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.toastService.showInfoToast(
          'nutrition.product-promotions.code-copied',
        );
      }
      this.promoCodeCopied.emit();
    } catch (error) {
      console.error('Failed to copy promo code:', error);
    }
  }

  visitStore() {
    window.open(this.config.storeUrl, '_blank');
    this.storeVisited.emit();
  }
}

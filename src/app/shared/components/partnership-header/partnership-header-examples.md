# Partnership Header Component Examples

## Basic Usage

```typescript
// Component TypeScript
partnershipConfig: PartnershipConfig = {
  logoSrc: '/assets/images/supplements/proof.png',
  logoAlt: 'Proof Nutrition',
  title: 'My Progress Guru си партнира с Proof Nutrition',
  description: 'Подкрепете развитието на нашето приложение и вземете 10% отстъпка',
  promoCode: 'MPG10',
  storeUrl: 'https://proofnutrition.eu'
};
```

```html
<!-- Component HTML -->
<mpg-partnership-header 
  [config]="partnershipConfig"
  (promoCodeCopied)="onPromoCodeCopied()"
  (storeVisited)="onStoreVisited()">
</mpg-partnership-header>
```

## Custom Gradient Colors

### Blue to Purple Gradient
```html
<mpg-partnership-header 
  [config]="partnershipConfig"
  gradientStart="#667eea"
  gradientEnd="#764ba2">
</mpg-partnership-header>
```

### Green to Teal Gradient
```html
<mpg-partnership-header 
  [config]="partnershipConfig"
  gradientStart="#11998e"
  gradientEnd="#38ef7d">
</mpg-partnership-header>
```

### Orange to Red Gradient
```html
<mpg-partnership-header 
  [config]="partnershipConfig"
  gradientStart="#ff9a9e"
  gradientEnd="#fecfef">
</mpg-partnership-header>
```

### Dark Theme Gradient
```html
<mpg-partnership-header 
  [config]="partnershipConfig"
  gradientStart="#2c3e50"
  gradientEnd="#34495e">
</mpg-partnership-header>
```

## Configuration Options

- `logoSrc`: Path to partner logo image
- `logoAlt`: Alt text for logo
- `title`: Partnership title text
- `description`: Partnership description
- `promoCode`: Promotional code to display
- `storeUrl`: URL to partner store
- `copyButtonText`: Custom text for copy button (optional)
- `visitStoreButtonText`: Custom text for visit store button (optional)
- `gradientStart`: Start color for gradient (optional, defaults to --ion-color-primary)
- `gradientEnd`: End color for gradient (optional, defaults to --ion-color-dark)

## Events

- `promoCodeCopied`: Emitted when promo code is copied
- `storeVisited`: Emitted when store link is clicked

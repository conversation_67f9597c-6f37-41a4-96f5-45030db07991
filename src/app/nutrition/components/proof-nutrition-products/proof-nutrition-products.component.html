<!-- Partnership Header -->
<mpg-partnership-header
  [config]="partnershipConfig"
  (promoCodeCopied)="onPromoCodeCopied()"
  (storeVisited)="onStoreVisited()">
</mpg-partnership-header>

<!-- Loading Spinner -->
<mpg-loading-spinner [isLoading]="isLoading"></mpg-loading-spinner>

<!-- Products Grid -->
<div *ngIf="!isLoading" class="products-grid">
  <div *ngFor="let product of products" class="product-card">
    <div class="product-image-container">
      <ion-img [alt]="product.name" [src]="product.imageUrl"></ion-img>
      <div class="discount-badge">
        -{{ product.discountPercentage }}%
      </div>
    </div>

    <div class="product-content">
      <div class="product-category">{{ product.category }}</div>
      <h3 class="product-name">{{ product.name }}</h3>
      <p class="product-description">{{ product.description }}</p>

      <!-- Features -->
      <div class="product-features">
        <ion-chip
          *ngFor="let feature of product.features"
          color="primary"
          outline="true"
          size="small">
          {{ feature }}
        </ion-chip>
      </div>

      <!-- Rating -->
      <div class="product-rating">
        <div class="stars">
          <ion-icon
            *ngFor="let star of getStarArray(product.rating)"
            [color]="star ? 'warning' : 'medium'"
            [name]="star ? 'star' : 'star-outline'">
          </ion-icon>
          <ion-icon
            *ngIf="hasHalfStar(product.rating)"
            color="warning"
            name="star-half">
          </ion-icon>
        </div>
        <span class="rating-text">{{ product.rating }} ({{ product.reviewsCount }})</span>
      </div>

      <!-- Price -->
      <div class="product-price">
        <span class="original-price">{{ product.price | currency:'BGN':'symbol':'1.2-2' }}</span>
        <span class="discount-price">{{ product.discountPrice | currency:'BGN':'symbol':'1.2-2' }}</span>
      </div>

      <!-- Promo Code Info -->
      <div class="promo-info">
        <span>с промокод MPG10</span>
      </div>

      <!-- Product Button -->
      <ion-button
        (click)="openProduct(product.productUrl)"
        class="product-button"
        color="primary"
        expand="block"
        fill="solid">
        <ion-icon name="storefront-outline" slot="start"></ion-icon>
        Виж продукта
      </ion-button>
    </div>
  </div>
</div>

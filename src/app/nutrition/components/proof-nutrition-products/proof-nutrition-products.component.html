<!-- Partnership Header -->
<div class="partnership-header">
  <div class="partnership-content">
    <div class="partnership-logo">
      <ion-img alt="Proof Nutrition" src="/assets/images/supplements/proof.png"></ion-img>
    </div>
    <div class="partnership-text">
      <h2>My Progress Guru си партнира с Proof Nutrition - научно-обосновани хранителни добавки!
      </h2>
      <p> Подкрепете развитието на нашето приложение и вземете 10% отстъпка с промокод MPG10 при следващата си поръчка
        от Proof Nutrition!</p>
    </div>
  </div>

  <!-- Promo Code Section -->
  <div class="promo-code-section">
    <div class="promo-code-card">
      <div class="promo-code-content">
        <span class="promo-label">Промокод:</span>
        <span class="promo-code">{{ promoCode }}</span>
      </div>
      <div class="promo-actions">
        <ion-button
          (click)="copyPromoCode()"
          color="light"
          fill="outline"
          size="small">
          <ion-icon name="copy-outline" slot="start"></ion-icon>
          Копирай кода
        </ion-button>
        <ion-button
          (click)="visitStore()"
          color="secondary"
          fill="solid"
          size="small">
          <ion-icon name="storefront-outline" slot="start"></ion-icon>
          Посети магазина
        </ion-button>
      </div>
    </div>
  </div>
</div>

<!-- Loading Spinner -->
<mpg-loading-spinner [isLoading]="isLoading"></mpg-loading-spinner>

<!-- Products Grid -->
<div *ngIf="!isLoading" class="products-grid">
  <div *ngFor="let product of products" class="product-card">
    <div class="product-image-container">
      <ion-img [alt]="product.name" [src]="product.imageUrl"></ion-img>
      <div class="discount-badge">
        -{{ product.discountPercentage }}%
      </div>
    </div>

    <div class="product-content">
      <div class="product-category">{{ product.category }}</div>
      <h3 class="product-name">{{ product.name }}</h3>
      <p class="product-description">{{ product.description }}</p>

      <!-- Features -->
      <div class="product-features">
        <ion-chip
          *ngFor="let feature of product.features"
          color="primary"
          outline="true"
          size="small">
          {{ feature }}
        </ion-chip>
      </div>

      <!-- Rating -->
      <div class="product-rating">
        <div class="stars">
          <ion-icon
            *ngFor="let star of getStarArray(product.rating)"
            [color]="star ? 'warning' : 'medium'"
            [name]="star ? 'star' : 'star-outline'">
          </ion-icon>
          <ion-icon
            *ngIf="hasHalfStar(product.rating)"
            color="warning"
            name="star-half">
          </ion-icon>
        </div>
        <span class="rating-text">{{ product.rating }} ({{ product.reviewsCount }})</span>
      </div>

      <!-- Price -->
      <div class="product-price">
        <span class="original-price">{{ product.price | currency:'BGN':'symbol':'1.2-2' }}</span>
        <span class="discount-price">{{ product.discountPrice | currency:'BGN':'symbol':'1.2-2' }}</span>
      </div>

      <!-- Promo Code Info -->
      <div class="promo-info">
        <span>с промокод MPG10</span>
      </div>

      <!-- Product Button -->
      <ion-button
        (click)="openProduct(product.productUrl)"
        class="product-button"
        color="primary"
        expand="block"
        fill="solid">
        <ion-icon name="storefront-outline" slot="start"></ion-icon>
        Виж продукта
      </ion-button>
    </div>
  </div>
</div>

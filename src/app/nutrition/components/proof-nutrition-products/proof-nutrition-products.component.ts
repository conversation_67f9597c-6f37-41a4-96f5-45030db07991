import { Component, OnInit } from '@angular/core';
import {
  MOCK_PROOF_NUTRITION_PRODUCTS,
  ProofNutritionProduct,
} from '../../models';
import { ToastService } from '../../../shared/services';
import { PartnershipConfig } from '../../../shared/components/partnership-header/partnership-header.model';

@Component({
  selector: 'mpg-proof-nutrition-products',
  templateUrl: './proof-nutrition-products.component.html',
  styleUrls: ['./proof-nutrition-products.component.scss'],
})
export class ProofNutritionProductsComponent implements OnInit {
  products: ProofNutritionProduct[] = [];
  promoCode = 'MPG10';
  isLoading = true;

  constructor(private toastService: ToastService) {}

  ngOnInit() {
    this.loadProducts();
  }

  async copyPromoCode() {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(this.promoCode);
        this.toastService.showInfoToast(
          'nutrition.product-promotions.code-copied',
        );
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = this.promoCode;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.toastService.showInfoToast(
          'nutrition.product-promotions.code-copied',
        );
      }
    } catch (error) {
      console.error('Failed to copy promo code:', error);
    }
  }

  visitStore() {
    window.open('https://proofnutrition.eu', '_blank');
  }

  openProduct(productUrl: string) {
    window.open(productUrl, '_blank');
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= Math.floor(rating));
    }
    return stars;
  }

  hasHalfStar(rating: number): boolean {
    return rating % 1 >= 0.5;
  }

  private loadProducts() {
    // Simulate API call
    setTimeout(() => {
      // Apply MPG10 discount to all products
      this.products = MOCK_PROOF_NUTRITION_PRODUCTS.map((product) => ({
        ...product,
        discountPrice: Math.round(product.price * 0.9 * 100) / 100, // 10% discount
        discountPercentage: 10,
      }));
      this.isLoading = false;
    }, 1000);
  }
}

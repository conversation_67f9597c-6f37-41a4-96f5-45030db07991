import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProofNutritionProductsComponent } from './proof-nutrition-products.component';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { PartnershipHeaderModule } from '../../../shared/components/partnership-header/partnership-header.module';

@NgModule({
  declarations: [ProofNutritionProductsComponent],
  exports: [ProofNutritionProductsComponent],
  imports: [
    CommonModule,
    IonicModule,
    TranslateModule,
    LoadingSpinnerComponentModule,
    PartnershipHeaderModule,
  ],
})
export class ProofNutritionProductsModule {}

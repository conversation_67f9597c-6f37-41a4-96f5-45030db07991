.partnership-header {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-dark) 100%);
  color: white;
  padding: 24px 16px;
  margin-bottom: 16px;
  border-radius: 12px;

  .partnership-content {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;

    .partnership-logo {
      flex-shrink: 0;
      width: 150px;
      height: 150px;
      background: white;
      border-radius: 12px;
      overflow: hidden;

      ion-img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .partnership-text {
      flex: 1;

      h2 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0;
        font-size: 14px;
        opacity: 0.9;
        line-height: 1.4;
      }
    }
  }

  .promo-code-section {
    .promo-code-card {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 16px;
      border: 1px solid rgba(255, 255, 255, 0.2);

      .promo-code-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 12px;

        .promo-label {
          font-size: 14px;
          opacity: 0.9;
        }

        .promo-code {
          background: rgba(255, 255, 255, 0.2);
          padding: 8px 16px;
          border-radius: 8px;
          font-family: 'Courier New', monospace;
          font-weight: bold;
          font-size: 16px;
          letter-spacing: 1px;
        }
      }

      .promo-actions {
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;

        ion-button {
          --border-radius: 8px;
          height: 36px;
          font-size: 12px;
        }
      }
    }
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  padding: 0 16px;
  margin-bottom: 32px;

  .product-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .product-image-container {
      position: relative;
      height: 220px;
      overflow: hidden;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;

      ion-img {
        width: 100%;
        height: 100%;

        &::part(image) {
          width: 100%;
          height: 100%;
          object-fit: contain;
          object-position: center;
        }
      }

      .discount-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        background: #e74c3c;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
      }
    }

    .product-content {
      padding: 16px;

      .product-category {
        color: #667eea;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 8px;
      }

      .product-name {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: #2c3e50;
        line-height: 1.3;
      }

      .product-description {
        font-size: 14px;
        color: #7f8c8d;
        margin: 0 0 12px 0;
        line-height: 1.4;
      }

      .product-features {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 12px;

        ion-chip {
          --background: rgba(102, 126, 234, 0.1);
          --color: #667eea;
          font-size: 11px;
          height: 24px;
        }
      }

      .product-rating {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        .stars {
          display: flex;
          gap: 2px;

          ion-icon {
            font-size: 14px;
          }
        }

        .rating-text {
          font-size: 12px;
          color: #7f8c8d;
        }
      }

      .product-price {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        .original-price {
          font-size: 14px;
          color: #95a5a6;
          text-decoration: line-through;
        }

        .discount-price {
          font-size: 18px;
          font-weight: bold;
          color: #27ae60;
        }
      }

      .promo-info {
        margin-bottom: 12px;

        span {
          font-size: 12px;
          color: var(--ion-color-primary);
          font-weight: 500;
          font-style: italic;
        }
      }

      .product-button {
        --border-radius: 8px;
        margin-top: 8px;
        height: 40px;
        font-weight: 500;
      }
    }
  }
}

@media (max-width: 768px) {
  .partnership-header {
    .partnership-content {
      flex-direction: column;
      text-align: center;

      .partnership-logo {
        align-self: center;
      }
    }

    .promo-code-section {
      .promo-code-card {
        .promo-actions {
          flex-direction: column;

          ion-button {
            width: 100%;
          }
        }
      }
    }
  }

  .products-grid {
    grid-template-columns: 1fr;
    padding: 0 12px;
  }
}
